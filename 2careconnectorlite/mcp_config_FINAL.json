{"mcpServers": {"playwright": {"command": "npx", "args": ["@playwright/mcp"], "env": {"PATH": "/opt/homebrew/bin:/usr/local/bin:/usr/bin:/bin"}}, "supabase": {"command": "npx", "args": ["@modelcontextprotocol/server-supabase"], "env": {"PATH": "/opt/homebrew/bin:/usr/local/bin:/usr/bin:/bin", "SUPABASE_URL": "https://yekarqanirdkdckimpna.supabase.co", "SUPABASE_ANON_KEY": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inlla2FycWFuaXJka2Rja2ltcG5hIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzUzNTAwNzcsImV4cCI6MjA1MDkyNjA3N30.0Rx8jn-6X1YyebZ0EeFRz4h-4_HFZPG_c5VHWrVNL0M"}}}}