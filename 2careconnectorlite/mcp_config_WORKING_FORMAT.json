{"mcpServers": {"playwright": {"command": "npx", "args": ["-y", "@playwright/mcp"], "env": {}}, "supabase": {"command": "npx", "args": ["-y", "@supabase/mcp-server-supabase"], "env": {"SUPABASE_URL": "https://yekarqanirdkdckimpna.supabase.co", "SUPABASE_ANON_KEY": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inlla2FycWFuaXJka2Rja2ltcG5hIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzUzNTAwNzcsImV4cCI6MjA1MDkyNjA3N30.0Rx8jn-6X1YyebZ0EeFRz4h-4_HFZPG_c5VHWrVNL0M"}}, "puppeteer": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-puppeteer"], "env": {}}}}