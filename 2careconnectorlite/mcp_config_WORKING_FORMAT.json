{"mcpServers": {"playwright": {"command": "npx", "args": ["-y", "@playwright/mcp"], "env": {}}, "supabase": {"command": "npx", "args": ["-y", "@supabase/mcp-server-supabase"], "env": {"SUPABASE_ACCESS_TOKEN": "********************************************", "SUPABASE_URL": "https://yekarqanirdkdckimpna.supabase.co", "SUPABASE_PROJECT_REF": "yekarqanirdkdckimpna"}}, "puppeteer": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-puppeteer"], "env": {}}}}